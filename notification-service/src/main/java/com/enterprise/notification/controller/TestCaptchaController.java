package com.enterprise.notification.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.captcha.generator.CodeGenerator;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Random;

@Slf4j
@RestController
@RequestMapping("/")
@Validated
public class TestCaptchaController {

    @GetMapping("/captcha-next")
    public void generateCaptchaNext(HttpServletResponse response, HttpSession session) throws IOException {
        // 1. 创建验证码图片（线段干扰）
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(150, 50, 4, 20);

        // 2. 设置自定义验证码生成器，确保包含数字和字母
        captcha.setGenerator(new MixedCodeGenerator(4));

        // 3. 重新生成验证码
        captcha.createCode();

        // 4. 获取验证码
        String code = captcha.getCode();

        // 5. 存储验证码到session
        session.setAttribute("CAPTCHA_CODE", code);
        log.info("生成验证码: {}", code);

        // 6. 输出图片
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Expires", "0");
        captcha.write(response.getOutputStream());
    }


    @GetMapping("/captcha")
    public void generateCaptcha(HttpServletResponse response, HttpSession session) throws IOException {
        // 1. 创建验证码图片（线段干扰）
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(150, 50, 4, 20);

        // 2. 设置自定义验证码生成器，确保包含数字和字母
        captcha.setGenerator(new MixedCodeGenerator(4));

        // 3. 重新生成验证码
        captcha.createCode();

        // 4. 获取验证码
        String code = captcha.getCode();

        // 5. 存储验证码到session
        session.setAttribute("CAPTCHA_CODE", code);
        log.info("生成验证码: {}", code);

        // 6. 输出图片
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Expires", "0");
        captcha.write(response.getOutputStream());
    }

    /**
     * 自定义验证码生成器，确保生成的验证码包含数字和字母
     */
    private static class MixedCodeGenerator implements CodeGenerator {
        private final int length;
        private final Random random = new Random();

        // 数字字符
        private static final String NUMBERS = "0123456789";
        // 字母字符（大小写）
        private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        // 所有字符
        private static final String ALL_CHARS = NUMBERS + LETTERS;

        public MixedCodeGenerator(int length) {
            if (length < 2) {
                throw new IllegalArgumentException("验证码长度至少为2位才能保证包含数字和字母");
            }
            this.length = length;
        }

        @Override
        public String generate() {
            StringBuilder code = new StringBuilder();

            // 确保至少包含一个数字
            code.append(NUMBERS.charAt(random.nextInt(NUMBERS.length())));

            // 确保至少包含一个字母
            code.append(LETTERS.charAt(random.nextInt(LETTERS.length())));

            // 填充剩余位数
            for (int i = 2; i < length; i++) {
                code.append(ALL_CHARS.charAt(random.nextInt(ALL_CHARS.length())));
            }

            // 打乱字符顺序，避免数字和字母总是在固定位置
            return shuffleString(code.toString());
        }

        @Override
        public boolean verify(String code, String userInputCode) {
            if (code == null || userInputCode == null) {
                return false;
            }
            return code.equalsIgnoreCase(userInputCode);
        }

        /**
         * 打乱字符串字符顺序
         */
        private String shuffleString(String str) {
            char[] chars = str.toCharArray();

            for (int i = chars.length - 1; i > 0; i--) {
                int j = random.nextInt(i + 1);
                // 交换字符
                char temp = chars[i];
                chars[i] = chars[j];
                chars[j] = temp;
            }

            return new String(chars);
        }
    }


}
