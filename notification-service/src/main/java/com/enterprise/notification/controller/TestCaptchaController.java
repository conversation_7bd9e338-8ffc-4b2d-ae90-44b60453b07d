package com.enterprise.notification.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Random;

@Slf4j
@RestController
@RequestMapping("/")
@Validated
public class TestCaptchaController {

    @GetMapping("/captcha")
    public void generateCaptcha(HttpServletResponse response, HttpSession session) throws IOException {
        String code;
        LineCaptcha captcha;

        // 循环生成验证码，直到满足包含数字和字母的要求
        do {
            // 1. 创建验证码图片（线段干扰）
            captcha = CaptchaUtil.createLineCaptcha(150, 50, 4, 20);

            // 2. 获取生成的验证码
            code = captcha.getCode();

        } while (!isValidMixedCode(code));

        // 3. 存储验证码到session
        session.setAttribute("CAPTCHA_CODE", code);
        log.info("生成验证码: {}", code);

        // 4. 输出图片
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Expires", "0");
        captcha.write(response.getOutputStream());
    }

    /**
     * 验证验证码是否包含数字和字母
     *
     * @param code 验证码
     * @return 是否包含数字和字母
     */
    private boolean isValidMixedCode(String code) {
        if (code == null || code.length() != 4) {
            return false;
        }

        boolean hasDigit = false;
        boolean hasLetter = false;

        for (char c : code.toCharArray()) {
            if (Character.isDigit(c)) {
                hasDigit = true;
            } else if (Character.isLetter(c)) {
                hasLetter = true;
            }

            // 如果已经包含数字和字母，可以提前返回
            if (hasDigit && hasLetter) {
                return true;
            }
        }

        return hasDigit && hasLetter;
    }


}
