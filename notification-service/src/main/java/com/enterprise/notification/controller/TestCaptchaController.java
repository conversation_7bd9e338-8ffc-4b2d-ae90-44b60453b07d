package com.enterprise.notification.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/")
@Validated
public class TestCaptchaController {

    @GetMapping("/captcha")
    public void generateCaptcha(HttpServletResponse response, HttpSession session) throws IOException {
        // 1. 创建4位数字字母验证码（线段干扰）
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(150, 50, 4, 20);

        // 2. 获取并存储验证码
        String code = captcha.getCode(); // 如 "A1b3"
        session.setAttribute("CAPTCHA_CODE", code);

        // 3. 输出图片
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        captcha.write(response.getOutputStream());
    }
}
