package com.enterprise.notification.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.Random;

@Slf4j
@RestController
@RequestMapping("/")
@Validated
public class TestCaptchaController {

    @GetMapping("/captcha")
    public void generateCaptcha(HttpServletResponse response, HttpSession session) throws IOException {
        // 1. 生成确保包含数字和字母的4位验证码
        String code = generateMixedCaptchaCode(4);

        // 2. 创建验证码图片（线段干扰）
        LineCaptcha captcha = CaptchaUtil.createLineCaptcha(150, 50, 4, 20);
        // 设置自定义验证码内容
        captcha.setGenerator(() -> code);
        // 重新生成图片以使用自定义验证码
        captcha.createCode();

        // 3. 存储验证码到session
        session.setAttribute("CAPTCHA_CODE", code);
        log.info("生成验证码: {}", code);

        // 4. 输出图片
        response.setContentType("image/jpeg");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Expires", "0");
        captcha.write(response.getOutputStream());
    }

    /**
     * 生成确保包含数字和字母的验证码
     *
     * @param length 验证码长度
     * @return 包含数字和字母的验证码
     */
    private String generateMixedCaptchaCode(int length) {
        if (length < 2) {
            throw new IllegalArgumentException("验证码长度至少为2位才能保证包含数字和字母");
        }

        Random random = new Random();
        StringBuilder code = new StringBuilder();

        // 数字字符
        String numbers = "0123456789";
        // 字母字符（大小写）
        String letters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
        // 所有字符
        String allChars = numbers + letters;

        // 确保至少包含一个数字
        code.append(numbers.charAt(random.nextInt(numbers.length())));

        // 确保至少包含一个字母
        code.append(letters.charAt(random.nextInt(letters.length())));

        // 填充剩余位数
        for (int i = 2; i < length; i++) {
            code.append(allChars.charAt(random.nextInt(allChars.length())));
        }

        // 打乱字符顺序，避免数字和字母总是在固定位置
        return shuffleString(code.toString());
    }

    /**
     * 打乱字符串字符顺序
     *
     * @param str 原字符串
     * @return 打乱后的字符串
     */
    private String shuffleString(String str) {
        char[] chars = str.toCharArray();
        Random random = new Random();

        for (int i = chars.length - 1; i > 0; i--) {
            int j = random.nextInt(i + 1);
            // 交换字符
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }

        return new String(chars);
    }
}
