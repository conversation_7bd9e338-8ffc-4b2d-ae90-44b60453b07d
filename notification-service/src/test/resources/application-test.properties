# Test Configuration
server.port=8081

# H2 Database for Testing
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
spring.datasource.username=sa
spring.datasource.password=
spring.datasource.driver-class-name=org.h2.Driver

# H2 Console (for debugging)
spring.h2.console.enabled=true

# MyBatis Plus Configuration
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

# Logging
logging.level.com.enterprise.notification=DEBUG
logging.level.org.springframework.web=DEBUG

# Notification Service Configuration (Test)
notification.providers.sms.aliyun.enabled=false
notification.providers.sms.tencent.enabled=false
notification.providers.email.aws-ses.enabled=false
notification.providers.email.sendgrid.enabled=false
notification.providers.im.wechat-work.enabled=false
notification.providers.im.dingtalk.enabled=false

notification.template.variable-pattern=\\$\\{([^}]+)\\}
notification.template.cache-enabled=false
notification.retry.max-attempts=1
