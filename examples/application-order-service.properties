# Order Service Configuration Example
# 订单服务配置示例

# Server Configuration
server.port=8082
spring.application.name=order-service

# Database Configuration (Order Service's own database)
spring.datasource.url=**************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=password

# Notification Client Configuration
# 通知客户端配置
notification.client.enabled=true
notification.client.base-url=http://localhost:8080/notification-service
notification.client.connect-timeout=5000
notification.client.read-timeout=30000
notification.client.max-retries=3
notification.client.retry-interval=1000

# Logging
logging.level.com.enterprise.order=INFO
logging.level.com.enterprise.notification.client=DEBUG
