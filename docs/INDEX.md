# 通知平台文档索引

## 📚 核心文档

### 🚀 快速开始
- [项目概览](01-overview.md) - 项目整体介绍和架构说明
- [快速开始](02-quick-start.md) - 快速部署和使用指南
- [项目结构](PROJECT-STRUCTURE.md) - 详细的项目结构说明

### 📖 开发文档
- [API参考](03-api-reference.md) - 完整的API接口文档（含Swagger）
- [开发指南](04-development-guide.md) - 开发环境搭建和BFF架构说明
- [部署指南](05-deployment-guide.md) - 生产环境部署说明
- [配置指南](CONFIGURATION-GUIDE.md) - 详细的配置说明和示例

### 📝 实现总结
- [实现总结](COMPLETE-IMPLEMENTATION-SUMMARY.md) - 完整的功能实现总结（含邮件、短信、IM、Java8兼容性等）

### 🖥️ 前端文档
- [前端README](frontend/README.md) - 前端项目说明
- [前端开发指南](frontend/development-guide.md) - 前端开发环境和规范
- [API规范](frontend/api-specification.md) - 前端API调用规范
- [国际化配置](frontend/i18n-keys.md) - 多语言配置说明

## 📋 文档分类

### 按用途分类
- **用户文档**: 01-overview.md, 02-quick-start.md, 03-api-reference.md
- **开发文档**: 04-development-guide.md, CONFIGURATION-GUIDE.md, PROJECT-STRUCTURE.md
- **部署文档**: 05-deployment-guide.md
- **实现文档**: COMPLETE-IMPLEMENTATION-SUMMARY.md

### 按重要性分类
- **必读文档**: 01-overview.md, 02-quick-start.md, CONFIGURATION-GUIDE.md
- **开发必读**: 04-development-guide.md, PROJECT-STRUCTURE.md, COMPLETE-IMPLEMENTATION-SUMMARY.md
- **参考文档**: 03-api-reference.md

## 🔍 快速查找

### 我想了解项目
👉 [项目概览](01-overview.md) → [项目结构](PROJECT-STRUCTURE.md) → [快速开始](02-quick-start.md)

### 我想开发功能
👉 [开发指南](04-development-guide.md) → [配置指南](CONFIGURATION-GUIDE.md) → [API参考](03-api-reference.md)

### 我想部署项目
👉 [部署指南](05-deployment-guide.md) → [配置指南](CONFIGURATION-GUIDE.md) → [升级总结](UPGRADE-SUMMARY.md)

### 我想了解实现细节
👉 [实现总结](COMPLETE-IMPLEMENTATION-SUMMARY.md) - 包含所有功能的详细实现说明

### 我遇到了问题
👉 [配置指南](CONFIGURATION-GUIDE.md) → [开发指南](04-development-guide.md) → [实现总结](COMPLETE-IMPLEMENTATION-SUMMARY.md)

## 📅 文档更新记录

- **最新更新**: 真实API实现完成，所有Mock功能已替换为真实实现
- **重要更新**: Java8兼容性修复，配置文件中文注释完善
- **功能完成**: 邮件、短信、IM、站内信四大通知渠道全部实现

## 💡 使用建议

1. **新用户**: 从[项目概览](01-overview.md)开始，然后阅读[快速开始](02-quick-start.md)
2. **开发者**: 重点阅读[开发指南](04-development-guide.md)和[配置指南](CONFIGURATION-GUIDE.md)
3. **运维人员**: 重点阅读[部署指南](05-deployment-guide.md)和[升级总结](UPGRADE-SUMMARY.md)
4. **架构师**: 重点阅读[项目结构](PROJECT-STRUCTURE.md)和[完整实现总结](COMPLETE-IMPLEMENTATION-SUMMARY.md)

---

📝 **文档维护**: 所有文档都会持续更新，建议定期查看最新版本。
