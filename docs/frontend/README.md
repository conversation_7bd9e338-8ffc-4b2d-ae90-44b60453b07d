# 前端开发文档

## 📋 **文档目录**

### 🎨 **页面原型图**
- [模板管理页面](./prototypes/template-management.html) - 模板的增删改查和测试发送
- [收件人组管理页面](./prototypes/recipient-group-management.html) - 组和成员的完整管理（已优化）
- [收件人成员管理页面](./prototypes/member-management.html) - 独立的成员管理、跨组查看、批量操作 🆕
- [通知审计监控页面](./prototypes/notification-audit.html) - 记录查询、统计图表、重发操作
- [渠道管理页面](./prototypes/channel-management.html) - 渠道配置、状态监控、统计分析 🆕
- [站内信管理页面](./prototypes/in-app-message-management.html) - 站内信查看、状态管理、批量操作 🆕

### 📚 **技术文档**
- [API接口规范](./api-specification.md) - 完整的API文档和数据模型
- [前端开发指南](./development-guide.md) - 开发规范、最佳实践和技术栈

### 🔄 **交互流程图**
- [用户操作流程图](#用户操作流程图) - 完整的用户操作路径
- [API调用时序图](#api调用时序图) - 前后端交互流程
- [错误处理流程图](#错误处理流程图) - 异常情况处理机制

## 🚀 **快速开始**

### 1. 查看页面原型
```bash
# 在浏览器中打开原型文件
open docs/frontend/prototypes/template-management.html
open docs/frontend/prototypes/recipient-group-management.html
open docs/frontend/prototypes/notification-audit.html
```

### 2. 阅读技术规范
- 先阅读 [API接口规范](./api-specification.md) 了解后端接口
- 再阅读 [前端开发指南](./development-guide.md) 了解开发规范

### 3. 开始开发
1. 按照开发指南搭建项目结构
2. 参考原型图实现页面布局
3. 根据API规范对接后端接口
4. 实现国际化和响应式设计

## 🎯 **核心功能模块**

### 📝 **模板管理**
**功能特性**:
- ✅ 模板列表查询（支持搜索、筛选、分页）
- ✅ 模板创建（支持多渠道、变量占位符）
- ✅ 模板编辑（实时预览、语法检查）
- ✅ 模板删除（批量操作、安全确认）
- ✅ 测试发送（参数填写、结果反馈）

**页面结构**:
```
模板管理
├── 搜索筛选区
│   ├── 模板代码搜索
│   ├── 模板名称搜索
│   ├── 渠道筛选
│   └── 状态筛选
├── 操作按钮区
│   └── 新建模板
├── 数据表格区
│   ├── 模板信息展示
│   ├── 状态标签
│   └── 操作按钮
└── 分页组件
```

### 👥 **收件人组管理**
**功能特性**:
- ✅ 收件人组列表（支持搜索、筛选）
- ✅ 组详情查看（基本信息、成员列表）
- ✅ 组创建编辑（信息维护、权限设置）
- ✅ 成员管理（添加、编辑、删除、批量操作）
- ✅ 偏好设置（渠道偏好、联系方式）

**页面结构**:
```
收件人组管理
├── 统计概览
│   ├── 总组数
│   ├── 总成员数
│   ├── 启用组数
│   └── 活跃成员
├── 标签页切换
│   ├── 组列表视图
│   └── 组详情视图
└── 操作功能
    ├── 组管理
    └── 成员管理
```

### 👤 **收件人成员管理** 🆕
**功能特性**:
- ✅ 成员全局视图（跨组查看所有成员）
- ✅ 高级搜索筛选（用户ID、姓名、组、联系方式）
- ✅ 成员详情管理（查看、编辑、状态控制）
- ✅ 批量操作功能（分配组、设置渠道、状态管理）
- ✅ 导入导出功能（Excel/CSV格式支持）

**页面结构**:
```
收件人成员管理
├── 统计概览
│   ├── 总成员数
│   ├── 活跃成员
│   ├── 禁用成员
│   └── 关联组数
├── 视图切换
│   ├── 卡片视图
│   └── 表格视图
├── 高级搜索
│   ├── 用户信息筛选
│   ├── 组织筛选
│   └── 联系方式筛选
├── 成员展示
│   ├── 成员信息
│   ├── 所属组标签
│   ├── 偏好渠道
│   └── 联系方式
└── 批量操作
    ├── 批量分配组
    ├── 批量设置渠道
    ├── 批量状态管理
    └── 导入导出
```

### 📊 **通知审计监控**
**功能特性**:
- ✅ 通知记录查询（多维度搜索、时间范围）
- ✅ 统计数据展示（发送量、成功率、趋势图）
- ✅ 实时监控（状态更新、异常告警）
- ✅ 详情查看（完整信息、参数展示）
- ✅ 重发操作（失败重试、批量重发）

**页面结构**:
```
通知审计监控
├── 统计概览
│   ├── 今日发送量
│   ├── 成功数量
│   ├── 失败数量
│   └── 成功率
├── 图表分析
│   ├── 发送趋势图
│   ├── 渠道分布图
│   └── 模板使用排行
├── 标签页
│   ├── 通知记录
│   ├── 统计分析
│   └── 实时监控
└── 记录管理
    ├── 高级搜索
    ├── 详情查看
    └── 重发操作
```

### 📡 **渠道管理** 🆕
**功能特性**:
- ✅ 渠道列表查询（支持搜索、筛选）
- ✅ 渠道配置管理（新增、编辑、删除）
- ✅ 渠道状态控制（启用、禁用）
- ✅ 实时统计监控（发送量、成功率、延迟）
- ✅ 渠道测试功能（连通性测试、配置验证）

**页面结构**:
```
渠道管理
├── 统计概览
│   ├── 总渠道数
│   ├── 启用渠道
│   ├── 禁用渠道
│   └── 今日使用量
├── 视图切换
│   ├── 卡片视图
│   └── 表格视图
├── 渠道卡片
│   ├── 渠道信息
│   ├── 实时统计
│   └── 操作按钮
└── 渠道管理
    ├── 配置编辑
    ├── 状态控制
    └── 测试功能
```

### 💬 **站内信管理** 🆕
**功能特性**:
- ✅ 站内信列表查询（多条件筛选、分页）
- ✅ 消息状态管理（已读、未读标记）
- ✅ 批量操作功能（批量已读、批量删除）
- ✅ 消息详情查看（完整内容、元数据）
- ✅ 统计数据展示（总数、未读数、阅读率）

**页面结构**:
```
站内信管理
├── 统计概览
│   ├── 总消息数
│   ├── 未读消息
│   ├── 已读消息
│   └── 阅读率
├── 搜索筛选
│   ├── 用户筛选
│   ├── 状态筛选
│   └── 时间范围
├── 消息列表
│   ├── 消息卡片
│   ├── 状态标识
│   └── 操作按钮
└── 批量操作
    ├── 批量选择
    ├── 批量已读
    └── 批量删除
```

## 🌐 **国际化支持**

### 支持语言
- 🇨🇳 **简体中文** (zh-CN) - 默认语言
- 🇺🇸 **英语** (en-US) - 国际化支持

### 切换方式
- **URL参数**: `?lang=en` 或 `?lang=zh`
- **Header设置**: `Accept-Language: en-US`
- **界面切换**: 顶部导航栏语言切换按钮

### 国际化内容
- ✅ 界面文本（菜单、按钮、标签）
- ✅ 错误消息（API错误、验证错误）
- ✅ 提示信息（成功、警告、确认）
- ✅ 表单验证（字段验证、格式检查）

## 📱 **响应式设计**

### 断点设置
- **手机端**: < 768px
- **平板端**: 768px - 1024px
- **桌面端**: > 1024px

### 适配策略
- **表格适配**: 小屏幕转换为卡片布局
- **表单适配**: 响应式栅格系统
- **导航适配**: 移动端折叠菜单
- **操作适配**: 触摸友好的按钮尺寸

## 🔒 **安全和性能**

### 安全措施
- ✅ XSS防护（输入转义、内容过滤）
- ✅ CSRF防护（Token验证）
- ✅ 权限控制（角色验证、操作授权）
- ✅ 会话管理（超时处理、自动登出）

### 性能优化
- ✅ 懒加载（路由分割、组件按需加载）
- ✅ 缓存策略（API缓存、静态资源缓存）
- ✅ 分页加载（避免大数据量一次性加载）
- ✅ 防抖节流（搜索输入、按钮点击）

## 🛠️ **开发工具**

### 推荐工具
- **IDE**: VS Code / WebStorm
- **调试**: Chrome DevTools / React DevTools
- **API测试**: Postman / Swagger UI
- **版本控制**: Git / GitHub

### 开发插件
- **VS Code插件**:
  - ES7+ React/Redux/React-Native snippets
  - Auto Rename Tag
  - Prettier - Code formatter
  - ESLint
  - GitLens

## 📞 **技术支持**

### 联系方式
- **技术负责人**: Enterprise Team
- **邮箱**: <EMAIL>
- **文档更新**: 随代码版本同步更新

### 问题反馈
- **Bug报告**: 通过GitHub Issues提交
- **功能建议**: 通过邮件或会议讨论
- **文档问题**: 直接修改并提交PR

---

**开始开发**: 选择合适的技术栈，参考原型图和API文档，按照开发指南进行实现！
