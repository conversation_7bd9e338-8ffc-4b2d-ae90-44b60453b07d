<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收件人成员管理 - 通知平台后台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; line-height: 1.6; }
        
        /* 顶部导航 */
        .header { height: 60px; background: #001529; color: white; display: flex; align-items: center; padding: 0 24px; }
        .logo { font-size: 20px; font-weight: bold; }
        .header-right { margin-left: auto; display: flex; align-items: center; gap: 16px; }
        .lang-switch { background: #1890ff; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 14px; }
        
        /* 主容器 */
        .container { display: flex; min-height: calc(100vh - 60px); }
        
        /* 侧边栏 */
        .sidebar { width: 200px; background: white; border-right: 1px solid #f0f0f0; }
        .menu-item { padding: 14px 24px; cursor: pointer; border-bottom: 1px solid #f0f0f0; font-size: 14px; transition: all 0.3s; }
        .menu-item:hover { background: #e6f7ff; }
        .menu-item.active { background: #1890ff; color: white; }
        
        /* 内容区域 */
        .content { flex: 1; padding: 24px; }
        .page-header { margin-bottom: 24px; }
        .page-title { font-size: 24px; margin-bottom: 8px; color: #333; }
        .breadcrumb { color: #666; font-size: 14px; }
        
        /* 统计卡片 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .stat-card { background: white; border-radius: 8px; padding: 20px; text-align: center; box-shadow: 0 2px 8px rgba(0,0,0,0.06); transition: all 0.3s; position: relative; }
        .stat-card:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.12); }
        .stat-icon { font-size: 32px; margin-bottom: 12px; }
        .stat-number { font-size: 28px; font-weight: bold; margin-bottom: 8px; }
        .stat-label { font-size: 14px; color: #666; }
        .stat-trend { position: absolute; top: 16px; right: 16px; font-size: 12px; }
        .trend-up { color: #52c41a; }
        .trend-down { color: #ff4d4f; }
        
        .stat-total .stat-number { color: #1890ff; }
        .stat-active .stat-number { color: #52c41a; }
        .stat-inactive .stat-number { color: #ff4d4f; }
        .stat-groups .stat-number { color: #722ed1; }
        
        /* 工具栏 */
        .toolbar { background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px; box-shadow: 0 2px 8px rgba(0,0,0,0.06); }
        .toolbar-top { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; }
        .toolbar-actions { display: flex; gap: 12px; }
        .search-form { display: flex; gap: 12px; align-items: end; flex-wrap: wrap; }
        .form-item { display: flex; flex-direction: column; gap: 4px; min-width: 120px; }
        .form-item label { font-size: 12px; color: #666; font-weight: 500; }
        .form-input, .form-select { padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px; }
        .form-input:focus, .form-select:focus { outline: none; border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24,144,255,0.2); }
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-primary:hover { background: #40a9ff; }
        .btn-default { background: white; border: 1px solid #d9d9d9; color: #666; }
        .btn-default:hover { border-color: #1890ff; color: #1890ff; }
        .btn-success { background: #52c41a; color: white; }
        .btn-warning { background: #faad14; color: white; }
        .btn-danger { background: #ff4d4f; color: white; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }
        
        /* 批量操作栏 */
        .batch-toolbar { background: #e6f7ff; padding: 12px 16px; border-radius: 6px; margin-bottom: 16px; display: none; align-items: center; justify-content: space-between; }
        .batch-info { color: #1890ff; font-weight: 500; }
        .batch-actions { display: flex; gap: 8px; }
        
        /* 视图切换 */
        .view-toggle { display: flex; gap: 8px; }
        .view-btn { padding: 8px 12px; border: 1px solid #d9d9d9; background: white; cursor: pointer; border-radius: 6px; font-size: 14px; }
        .view-btn.active { background: #1890ff; color: white; border-color: #1890ff; }
        
        /* 成员卡片视图 */
        .members-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .member-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.06); transition: all 0.3s; position: relative; }
        .member-card:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.12); }
        .member-card.selected { border: 2px solid #1890ff; }
        
        .member-header { display: flex; align-items: center; gap: 12px; margin-bottom: 16px; }
        .member-checkbox { margin-right: 8px; }
        .member-avatar { width: 48px; height: 48px; border-radius: 50%; background: linear-gradient(135deg, #1890ff, #40a9ff); color: white; display: flex; align-items: center; justify-content: center; font-size: 16px; font-weight: bold; }
        .member-info { flex: 1; }
        .member-name { font-size: 16px; font-weight: 500; color: #333; margin-bottom: 4px; }
        .member-id { font-size: 12px; color: #666; font-family: monospace; background: #f5f5f5; padding: 2px 6px; border-radius: 4px; }
        .member-status { }
        
        .member-details { margin-bottom: 16px; }
        .detail-row { display: flex; align-items: center; gap: 8px; margin-bottom: 8px; font-size: 13px; }
        .detail-icon { width: 16px; text-align: center; color: #666; }
        .detail-value { color: #333; }
        
        .member-groups { margin-bottom: 16px; }
        .groups-label { font-size: 12px; color: #666; margin-bottom: 8px; font-weight: 500; }
        .group-tags { display: flex; gap: 6px; flex-wrap: wrap; }
        .group-tag { padding: 4px 8px; background: #f0f0f0; border-radius: 12px; font-size: 11px; color: #666; }
        
        .member-channels { margin-bottom: 16px; }
        .channels-label { font-size: 12px; color: #666; margin-bottom: 8px; font-weight: 500; }
        .channel-tags { display: flex; gap: 6px; flex-wrap: wrap; }
        .channel-tag { padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 500; }
        .channel-inapp { background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff; }
        .channel-sms { background: #fff2e8; color: #fa8c16; border: 1px solid #ffd591; }
        .channel-email { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .channel-im { background: #f9f0ff; color: #722ed1; border: 1px solid #d3adf7; }
        
        .member-actions { display: flex; gap: 8px; }
        .action-btn { padding: 6px 12px; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; }
        .action-btn.edit { background: #e6f7ff; color: #1890ff; }
        .action-btn.delete { background: #fff2f0; color: #ff4d4f; }
        
        /* 表格视图 */
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.06); }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 16px; text-align: left; border-bottom: 1px solid #f0f0f0; }
        .table th { background: #fafafa; font-weight: 500; color: #666; font-size: 14px; }
        .table tr:hover { background: #fafafa; }
        
        /* 状态标签 */
        .status-tag { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-enabled { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .status-disabled { background: #fff2f0; color: #ff4d4f; border: 1px solid #ffccc7; }
        
        /* 分页 */
        .pagination { margin-top: 16px; text-align: right; padding: 16px; background: white; border-radius: 8px; }
        
        /* 模态框 */
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; width: 600px; max-height: 80vh; overflow-y: auto; }
        .modal-header { padding: 20px 24px; border-bottom: 1px solid #f0f0f0; display: flex; justify-content: space-between; align-items: center; }
        .modal-body { padding: 24px; }
        .modal-footer { padding: 16px 24px; border-top: 1px solid #f0f0f0; text-align: right; }
        
        /* 表单 */
        .form-row { margin-bottom: 20px; }
        .form-label { display: block; margin-bottom: 8px; font-weight: 500; color: #333; }
        .form-control { width: 100%; padding: 10px 12px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px; }
        .form-control:focus { outline: none; border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24,144,255,0.2); }
        .form-textarea { min-height: 80px; resize: vertical; }
        .form-checkbox-group { display: flex; gap: 12px; flex-wrap: wrap; }
        .form-checkbox-item { display: flex; align-items: center; gap: 6px; }
        
        /* 响应式设计 */
        @media (max-width: 1024px) {
            .content { padding: 16px; }
            .toolbar-top { flex-direction: column; gap: 16px; align-items: stretch; }
            .search-form { flex-direction: column; align-items: stretch; }
            .form-item { min-width: auto; }
        }
        
        @media (max-width: 768px) {
            .container { flex-direction: column; }
            .sidebar { width: 100%; }
            .stats-grid { grid-template-columns: repeat(2, 1fr); }
            .members-grid { grid-template-columns: 1fr; }
            .table-container { overflow-x: auto; }
            .table { min-width: 800px; }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="logo">🔔 通知平台管理后台</div>
        <div class="header-right">
            <button class="lang-switch">中文 / EN</button>
            <span>管理员</span>
            <button class="btn-link">退出</button>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="menu-item">📝 模板管理</div>
            <div class="menu-item">👥 收件人组管理</div>
            <div class="menu-item active">👤 成员管理</div>
            <div class="menu-item">📊 通知审计</div>
            <div class="menu-item">📡 渠道管理</div>
            <div class="menu-item">💬 站内信管理</div>
            <div class="menu-item">⚙️ 系统设置</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 页面头部 -->
            <div class="page-header">
                <h1 class="page-title">收件人成员管理</h1>
                <div class="breadcrumb">首页 / 收件人成员管理</div>
            </div>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card stat-total">
                    <div class="stat-trend trend-up">↗ +8%</div>
                    <div class="stat-icon">👥</div>
                    <div class="stat-number">1,256</div>
                    <div class="stat-label">总成员数</div>
                </div>
                <div class="stat-card stat-active">
                    <div class="stat-trend trend-up">↗ +5%</div>
                    <div class="stat-icon">✅</div>
                    <div class="stat-number">1,198</div>
                    <div class="stat-label">活跃成员</div>
                </div>
                <div class="stat-card stat-inactive">
                    <div class="stat-trend trend-down">↘ -2%</div>
                    <div class="stat-icon">❌</div>
                    <div class="stat-number">58</div>
                    <div class="stat-label">禁用成员</div>
                </div>
                <div class="stat-card stat-groups">
                    <div class="stat-trend trend-up">↗ +1</div>
                    <div class="stat-icon">🏷️</div>
                    <div class="stat-number">8</div>
                    <div class="stat-label">关联组数</div>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="toolbar-top">
                    <div class="toolbar-actions">
                        <button class="btn btn-primary">➕ 新增成员</button>
                        <button class="btn btn-success">📥 导入成员</button>
                        <button class="btn btn-default">📤 导出成员</button>
                    </div>
                    <div class="view-toggle">
                        <button class="view-btn active" onclick="showCardView()">🎯 卡片视图</button>
                        <button class="view-btn" onclick="showTableView()">📋 表格视图</button>
                    </div>
                </div>
                
                <div class="search-form">
                    <div class="form-item">
                        <label>用户ID</label>
                        <input type="text" class="form-input" placeholder="请输入用户ID">
                    </div>
                    <div class="form-item">
                        <label>用户名称</label>
                        <input type="text" class="form-input" placeholder="请输入用户名称">
                    </div>
                    <div class="form-item">
                        <label>所属组</label>
                        <select class="form-select">
                            <option value="">全部组</option>
                            <option value="ADMIN_GROUP">系统管理员组</option>
                            <option value="OPS_TEAM">运维团队</option>
                            <option value="DEV_TEAM">开发团队</option>
                            <option value="PRODUCT_TEAM">产品团队</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label>手机号</label>
                        <input type="text" class="form-input" placeholder="请输入手机号">
                    </div>
                    <div class="form-item">
                        <label>邮箱</label>
                        <input type="text" class="form-input" placeholder="请输入邮箱">
                    </div>
                    <div class="form-item">
                        <label>状态</label>
                        <select class="form-select">
                            <option value="">全部状态</option>
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label>&nbsp;</label>
                        <button class="btn btn-primary">🔍 搜索</button>
                    </div>
                    <div class="form-item">
                        <label>&nbsp;</label>
                        <button class="btn btn-default">🔄 重置</button>
                    </div>
                </div>
            </div>

            <!-- 批量操作栏 -->
            <div class="batch-toolbar" id="batchToolbar">
                <div class="batch-info">
                    已选择 <span id="selectedCount">0</span> 个成员
                </div>
                <div class="batch-actions">
                    <button class="btn btn-success btn-sm">🏷️ 批量分配组</button>
                    <button class="btn btn-warning btn-sm">📡 批量设置偏好渠道</button>
                    <button class="btn btn-default btn-sm">✅ 批量启用</button>
                    <button class="btn btn-default btn-sm">❌ 批量禁用</button>
                    <button class="btn btn-danger btn-sm">🗑️ 批量删除</button>
                </div>
            </div>

            <!-- 卡片视图 -->
            <div id="cardView" class="members-grid">
                <!-- 成员卡片1 -->
                <div class="member-card">
                    <div class="member-header">
                        <input type="checkbox" class="member-checkbox" onchange="toggleSelection(this)">
                        <div class="member-avatar">张</div>
                        <div class="member-info">
                            <div class="member-name">张三 - 系统管理员</div>
                            <div class="member-id">admin001</div>
                        </div>
                        <div class="member-status">
                            <span class="status-tag status-enabled">启用</span>
                        </div>
                    </div>
                    
                    <div class="member-details">
                        <div class="detail-row">
                            <span class="detail-icon">📱</span>
                            <span class="detail-value">13800138000</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📧</span>
                            <span class="detail-value"><EMAIL></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">💬</span>
                            <span class="detail-value">zhangsan</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📅</span>
                            <span class="detail-value">2024-01-15 10:30:00</span>
                        </div>
                    </div>
                    
                    <div class="member-groups">
                        <div class="groups-label">所属组</div>
                        <div class="group-tags">
                            <span class="group-tag">系统管理员组</span>
                            <span class="group-tag">全体员工</span>
                        </div>
                    </div>
                    
                    <div class="member-channels">
                        <div class="channels-label">偏好渠道</div>
                        <div class="channel-tags">
                            <span class="channel-tag channel-inapp">站内信</span>
                            <span class="channel-tag channel-email">邮件</span>
                            <span class="channel-tag channel-im">IM</span>
                        </div>
                    </div>
                    
                    <div class="member-actions">
                        <button class="action-btn edit" onclick="editMember(1)">✏️ 编辑</button>
                        <button class="action-btn" onclick="viewMember(1)">👁️ 查看</button>
                        <button class="action-btn delete" onclick="deleteMember(1)">🗑️ 删除</button>
                    </div>
                </div>

                <!-- 成员卡片2 -->
                <div class="member-card">
                    <div class="member-header">
                        <input type="checkbox" class="member-checkbox" onchange="toggleSelection(this)">
                        <div class="member-avatar">李</div>
                        <div class="member-info">
                            <div class="member-name">李四 - 安全管理员</div>
                            <div class="member-id">admin002</div>
                        </div>
                        <div class="member-status">
                            <span class="status-tag status-enabled">启用</span>
                        </div>
                    </div>
                    
                    <div class="member-details">
                        <div class="detail-row">
                            <span class="detail-icon">📱</span>
                            <span class="detail-value">13800138001</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📧</span>
                            <span class="detail-value"><EMAIL></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">💬</span>
                            <span class="detail-value">lisi</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📅</span>
                            <span class="detail-value">2024-01-15 10:30:00</span>
                        </div>
                    </div>
                    
                    <div class="member-groups">
                        <div class="groups-label">所属组</div>
                        <div class="group-tags">
                            <span class="group-tag">系统管理员组</span>
                            <span class="group-tag">全体员工</span>
                        </div>
                    </div>
                    
                    <div class="member-channels">
                        <div class="channels-label">偏好渠道</div>
                        <div class="channel-tags">
                            <span class="channel-tag channel-inapp">站内信</span>
                            <span class="channel-tag channel-email">邮件</span>
                            <span class="channel-tag channel-sms">短信</span>
                            <span class="channel-tag channel-im">IM</span>
                        </div>
                    </div>
                    
                    <div class="member-actions">
                        <button class="action-btn edit" onclick="editMember(2)">✏️ 编辑</button>
                        <button class="action-btn" onclick="viewMember(2)">👁️ 查看</button>
                        <button class="action-btn delete" onclick="deleteMember(2)">🗑️ 删除</button>
                    </div>
                </div>

                <!-- 成员卡片3 -->
                <div class="member-card">
                    <div class="member-header">
                        <input type="checkbox" class="member-checkbox" onchange="toggleSelection(this)">
                        <div class="member-avatar">王</div>
                        <div class="member-info">
                            <div class="member-name">王五 - 运维工程师</div>
                            <div class="member-id">ops001</div>
                        </div>
                        <div class="member-status">
                            <span class="status-tag status-enabled">启用</span>
                        </div>
                    </div>
                    
                    <div class="member-details">
                        <div class="detail-row">
                            <span class="detail-icon">📱</span>
                            <span class="detail-value">13800138002</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📧</span>
                            <span class="detail-value"><EMAIL></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">💬</span>
                            <span class="detail-value">wangwu</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📅</span>
                            <span class="detail-value">2024-01-15 09:15:00</span>
                        </div>
                    </div>
                    
                    <div class="member-groups">
                        <div class="groups-label">所属组</div>
                        <div class="group-tags">
                            <span class="group-tag">运维团队</span>
                            <span class="group-tag">全体员工</span>
                        </div>
                    </div>
                    
                    <div class="member-channels">
                        <div class="channels-label">偏好渠道</div>
                        <div class="channel-tags">
                            <span class="channel-tag channel-inapp">站内信</span>
                            <span class="channel-tag channel-sms">短信</span>
                            <span class="channel-tag channel-im">IM</span>
                        </div>
                    </div>
                    
                    <div class="member-actions">
                        <button class="action-btn edit" onclick="editMember(3)">✏️ 编辑</button>
                        <button class="action-btn" onclick="viewMember(3)">👁️ 查看</button>
                        <button class="action-btn delete" onclick="deleteMember(3)">🗑️ 删除</button>
                    </div>
                </div>

                <!-- 成员卡片4 -->
                <div class="member-card">
                    <div class="member-header">
                        <input type="checkbox" class="member-checkbox" onchange="toggleSelection(this)">
                        <div class="member-avatar">赵</div>
                        <div class="member-info">
                            <div class="member-name">赵六 - 前端工程师</div>
                            <div class="member-id">dev001</div>
                        </div>
                        <div class="member-status">
                            <span class="status-tag status-disabled">禁用</span>
                        </div>
                    </div>
                    
                    <div class="member-details">
                        <div class="detail-row">
                            <span class="detail-icon">📱</span>
                            <span class="detail-value">13800138005</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📧</span>
                            <span class="detail-value"><EMAIL></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">💬</span>
                            <span class="detail-value">zhaoliu</span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-icon">📅</span>
                            <span class="detail-value">2024-01-14 16:45:00</span>
                        </div>
                    </div>
                    
                    <div class="member-groups">
                        <div class="groups-label">所属组</div>
                        <div class="group-tags">
                            <span class="group-tag">开发团队</span>
                            <span class="group-tag">全体员工</span>
                        </div>
                    </div>
                    
                    <div class="member-channels">
                        <div class="channels-label">偏好渠道</div>
                        <div class="channel-tags">
                            <span class="channel-tag channel-inapp">站内信</span>
                            <span class="channel-tag channel-email">邮件</span>
                        </div>
                    </div>
                    
                    <div class="member-actions">
                        <button class="action-btn edit" onclick="editMember(4)">✏️ 编辑</button>
                        <button class="action-btn" onclick="viewMember(4)">👁️ 查看</button>
                        <button class="action-btn delete" onclick="deleteMember(4)">🗑️ 删除</button>
                    </div>
                </div>
            </div>

            <!-- 表格视图 -->
            <div id="tableView" class="table-container" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th><input type="checkbox" onchange="toggleSelectAll(this)"></th>
                            <th>成员信息</th>
                            <th>联系方式</th>
                            <th>所属组</th>
                            <th>偏好渠道</th>
                            <th>状态</th>
                            <th>加入时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" onchange="toggleSelection(this)"></td>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="member-avatar" style="width: 32px; height: 32px; font-size: 12px;">张</div>
                                    <div>
                                        <div style="font-weight: 500;">张三 - 系统管理员</div>
                                        <div style="font-size: 12px; color: #666;">admin001</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 13px;">
                                    <div>📱 13800138000</div>
                                    <div>📧 <EMAIL></div>
                                    <div>💬 zhangsan</div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="group-tag">系统管理员组</span>
                                    <span class="group-tag">全体员工</span>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="channel-tag channel-inapp">站内信</span>
                                    <span class="channel-tag channel-email">邮件</span>
                                    <span class="channel-tag channel-im">IM</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">查看</button>
                                    <button class="btn btn-link btn-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" onchange="toggleSelection(this)"></td>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="member-avatar" style="width: 32px; height: 32px; font-size: 12px;">李</div>
                                    <div>
                                        <div style="font-weight: 500;">李四 - 安全管理员</div>
                                        <div style="font-size: 12px; color: #666;">admin002</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 13px;">
                                    <div>📱 13800138001</div>
                                    <div>📧 <EMAIL></div>
                                    <div>💬 lisi</div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="group-tag">系统管理员组</span>
                                    <span class="group-tag">全体员工</span>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="channel-tag channel-inapp">站内信</span>
                                    <span class="channel-tag channel-email">邮件</span>
                                    <span class="channel-tag channel-sms">短信</span>
                                    <span class="channel-tag channel-im">IM</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">查看</button>
                                    <button class="btn btn-link btn-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" onchange="toggleSelection(this)"></td>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="member-avatar" style="width: 32px; height: 32px; font-size: 12px;">王</div>
                                    <div>
                                        <div style="font-weight: 500;">王五 - 运维工程师</div>
                                        <div style="font-size: 12px; color: #666;">ops001</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 13px;">
                                    <div>📱 13800138002</div>
                                    <div>📧 <EMAIL></div>
                                    <div>💬 wangwu</div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="group-tag">运维团队</span>
                                    <span class="group-tag">全体员工</span>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="channel-tag channel-inapp">站内信</span>
                                    <span class="channel-tag channel-sms">短信</span>
                                    <span class="channel-tag channel-im">IM</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>2024-01-15 09:15:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">查看</button>
                                    <button class="btn btn-link btn-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" onchange="toggleSelection(this)"></td>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="member-avatar" style="width: 32px; height: 32px; font-size: 12px;">赵</div>
                                    <div>
                                        <div style="font-weight: 500;">赵六 - 前端工程师</div>
                                        <div style="font-size: 12px; color: #666;">dev001</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div style="font-size: 13px;">
                                    <div>📱 13800138005</div>
                                    <div>📧 <EMAIL></div>
                                    <div>💬 zhaoliu</div>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="group-tag">开发团队</span>
                                    <span class="group-tag">全体员工</span>
                                </div>
                            </td>
                            <td>
                                <div style="display: flex; gap: 4px; flex-wrap: wrap;">
                                    <span class="channel-tag channel-inapp">站内信</span>
                                    <span class="channel-tag channel-email">邮件</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-disabled">禁用</span></td>
                            <td>2024-01-14 16:45:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">查看</button>
                                    <button class="btn btn-link btn-sm">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div class="pagination">
                <span>共 1,256 条记录，第 1/126 页</span>
                <button class="btn btn-default btn-sm">上一页</button>
                <button class="btn btn-primary btn-sm">1</button>
                <button class="btn btn-default btn-sm">2</button>
                <button class="btn btn-default btn-sm">3</button>
                <button class="btn btn-default btn-sm">下一页</button>
            </div>
        </div>
    </div>

    <!-- 新增/编辑成员模态框 -->
    <div class="modal" id="memberModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增成员</h3>
                <button onclick="closeModal()" style="background: none; border: none; font-size: 20px; cursor: pointer;">×</button>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label class="form-label">用户ID *</label>
                    <input type="text" class="form-control" placeholder="请输入用户ID">
                </div>
                <div class="form-row">
                    <label class="form-label">用户名称 *</label>
                    <input type="text" class="form-control" placeholder="请输入用户名称">
                </div>
                <div class="form-row">
                    <label class="form-label">手机号</label>
                    <input type="text" class="form-control" placeholder="请输入手机号">
                </div>
                <div class="form-row">
                    <label class="form-label">邮箱</label>
                    <input type="email" class="form-control" placeholder="请输入邮箱">
                </div>
                <div class="form-row">
                    <label class="form-label">IM账号</label>
                    <input type="text" class="form-control" placeholder="请输入IM账号">
                </div>
                <div class="form-row">
                    <label class="form-label">所属组 *</label>
                    <div class="form-checkbox-group">
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="group1" value="ADMIN_GROUP">
                            <label for="group1">系统管理员组</label>
                        </div>
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="group2" value="OPS_TEAM">
                            <label for="group2">运维团队</label>
                        </div>
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="group3" value="DEV_TEAM">
                            <label for="group3">开发团队</label>
                        </div>
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="group4" value="ALL_EMPLOYEES">
                            <label for="group4">全体员工</label>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <label class="form-label">偏好渠道</label>
                    <div class="form-checkbox-group">
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="channel1" value="IN_APP">
                            <label for="channel1">站内信</label>
                        </div>
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="channel2" value="SMS">
                            <label for="channel2">短信</label>
                        </div>
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="channel3" value="EMAIL">
                            <label for="channel3">邮件</label>
                        </div>
                        <div class="form-checkbox-item">
                            <input type="checkbox" id="channel4" value="IM">
                            <label for="channel4">企业IM</label>
                        </div>
                    </div>
                </div>
                <div class="form-row">
                    <label class="form-label">
                        <input type="checkbox" checked> 启用成员
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default" onclick="closeModal()">取消</button>
                <button class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script>
        let selectedCount = 0;

        function showCardView() {
            document.getElementById('cardView').style.display = 'grid';
            document.getElementById('tableView').style.display = 'none';
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function toggleSelection(checkbox) {
            if (checkbox.checked) {
                selectedCount++;
                checkbox.closest('.member-card, tr').classList.add('selected');
            } else {
                selectedCount--;
                checkbox.closest('.member-card, tr').classList.remove('selected');
            }
            
            document.getElementById('selectedCount').textContent = selectedCount;
            document.getElementById('batchToolbar').style.display = selectedCount > 0 ? 'flex' : 'none';
        }

        function toggleSelectAll(checkbox) {
            const checkboxes = document.querySelectorAll('.member-checkbox, td input[type="checkbox"]');
            checkboxes.forEach(cb => {
                cb.checked = checkbox.checked;
                toggleSelection(cb);
            });
        }

        function editMember(id) {
            console.log('编辑成员:', id);
            document.getElementById('memberModal').style.display = 'block';
        }

        function viewMember(id) {
            console.log('查看成员:', id);
        }

        function deleteMember(id) {
            if (confirm('确定要删除这个成员吗？')) {
                console.log('删除成员:', id);
            }
        }

        function closeModal() {
            document.getElementById('memberModal').style.display = 'none';
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modal = document.getElementById('memberModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>
