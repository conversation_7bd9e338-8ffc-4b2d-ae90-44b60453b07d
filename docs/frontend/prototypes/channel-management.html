<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>渠道管理 - 通知平台后台</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f5f5; }
        
        /* 顶部导航 */
        .header { height: 60px; background: #001529; color: white; display: flex; align-items: center; padding: 0 24px; }
        .logo { font-size: 20px; font-weight: bold; }
        .header-right { margin-left: auto; display: flex; align-items: center; gap: 16px; }
        .lang-switch { background: #1890ff; border: none; color: white; padding: 4px 12px; border-radius: 4px; cursor: pointer; }
        
        /* 主容器 */
        .container { display: flex; min-height: calc(100vh - 60px); }
        
        /* 侧边栏 */
        .sidebar { width: 200px; background: white; border-right: 1px solid #f0f0f0; }
        .menu-item { padding: 12px 24px; cursor: pointer; border-bottom: 1px solid #f0f0f0; }
        .menu-item:hover { background: #e6f7ff; }
        .menu-item.active { background: #1890ff; color: white; }
        
        /* 内容区域 */
        .content { flex: 1; padding: 24px; }
        .page-header { margin-bottom: 24px; }
        .page-title { font-size: 24px; margin-bottom: 8px; }
        .breadcrumb { color: #666; font-size: 14px; }
        
        /* 统计卡片 */
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .stat-card { background: white; border-radius: 8px; padding: 20px; text-align: center; position: relative; box-shadow: 0 2px 8px rgba(0,0,0,0.06); }
        .stat-icon { font-size: 32px; margin-bottom: 12px; }
        .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 8px; }
        .stat-label { font-size: 14px; color: #666; }
        .stat-trend { position: absolute; top: 16px; right: 16px; font-size: 12px; }
        .trend-up { color: #52c41a; }
        .trend-stable { color: #faad14; }
        
        .stat-total .stat-number { color: #1890ff; }
        .stat-enabled .stat-number { color: #52c41a; }
        .stat-disabled .stat-number { color: #ff4d4f; }
        .stat-usage .stat-number { color: #722ed1; }
        
        /* 工具栏 */
        .toolbar { background: white; padding: 16px; border-radius: 8px; margin-bottom: 16px; display: flex; justify-content: space-between; align-items: center; box-shadow: 0 2px 8px rgba(0,0,0,0.06); }
        .search-form { display: flex; gap: 12px; align-items: center; }
        .form-item { display: flex; flex-direction: column; gap: 4px; }
        .form-item label { font-size: 12px; color: #666; font-weight: 500; }
        .form-input, .form-select { padding: 8px 12px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px; }
        .form-input:focus, .form-select:focus { outline: none; border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24,144,255,0.2); }
        .btn { padding: 8px 16px; border: none; border-radius: 6px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s; }
        .btn-primary { background: #1890ff; color: white; }
        .btn-primary:hover { background: #40a9ff; }
        .btn-default { background: white; border: 1px solid #d9d9d9; color: #666; }
        .btn-default:hover { border-color: #1890ff; color: #1890ff; }
        .btn-success { background: #52c41a; color: white; }
        .btn-danger { background: #ff4d4f; color: white; }
        
        /* 渠道卡片网格 */
        .channels-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 16px; margin-bottom: 24px; }
        .channel-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 8px rgba(0,0,0,0.06); transition: all 0.3s; }
        .channel-card:hover { box-shadow: 0 4px 16px rgba(0,0,0,0.12); }
        
        .channel-header { display: flex; align-items: center; justify-content: space-between; margin-bottom: 16px; }
        .channel-info { display: flex; align-items: center; gap: 12px; }
        .channel-icon { width: 48px; height: 48px; border-radius: 8px; display: flex; align-items: center; justify-content: center; font-size: 20px; color: white; }
        .channel-icon.inapp { background: linear-gradient(135deg, #1890ff, #40a9ff); }
        .channel-icon.sms { background: linear-gradient(135deg, #fa8c16, #ffa940); }
        .channel-icon.email { background: linear-gradient(135deg, #52c41a, #73d13d); }
        .channel-icon.im { background: linear-gradient(135deg, #722ed1, #9254de); }
        
        .channel-details h3 { font-size: 16px; margin-bottom: 4px; }
        .channel-code { font-size: 12px; color: #666; font-family: monospace; background: #f5f5f5; padding: 2px 6px; border-radius: 4px; }
        
        .channel-status { display: flex; align-items: center; gap: 8px; }
        .status-switch { position: relative; width: 44px; height: 24px; background: #ccc; border-radius: 12px; cursor: pointer; transition: all 0.3s; }
        .status-switch.enabled { background: #52c41a; }
        .status-switch::after { content: ''; position: absolute; top: 2px; left: 2px; width: 20px; height: 20px; background: white; border-radius: 50%; transition: all 0.3s; }
        .status-switch.enabled::after { left: 22px; }
        
        .channel-stats { display: grid; grid-template-columns: repeat(3, 1fr); gap: 12px; margin-bottom: 16px; }
        .stat-item { text-align: center; padding: 8px; background: #fafafa; border-radius: 6px; }
        .stat-item-number { font-size: 16px; font-weight: bold; color: #1890ff; }
        .stat-item-label { font-size: 11px; color: #666; margin-top: 2px; }
        
        .channel-actions { display: flex; gap: 8px; }
        .btn-sm { padding: 6px 12px; font-size: 12px; }
        
        /* 表格视图 */
        .table-container { background: white; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.06); }
        .table { width: 100%; border-collapse: collapse; }
        .table th, .table td { padding: 16px; text-align: left; border-bottom: 1px solid #f0f0f0; }
        .table th { background: #fafafa; font-weight: 500; color: #666; font-size: 14px; }
        .table tr:hover { background: #fafafa; }
        
        /* 状态标签 */
        .status-tag { padding: 4px 8px; border-radius: 12px; font-size: 12px; font-weight: 500; }
        .status-enabled { background: #f6ffed; color: #52c41a; border: 1px solid #b7eb8f; }
        .status-disabled { background: #fff2f0; color: #ff4d4f; border: 1px solid #ffccc7; }
        
        /* 视图切换 */
        .view-toggle { display: flex; gap: 8px; }
        .view-btn { padding: 8px 12px; border: 1px solid #d9d9d9; background: white; cursor: pointer; border-radius: 6px; }
        .view-btn.active { background: #1890ff; color: white; border-color: #1890ff; }
        
        /* 模态框 */
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal-content { position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 8px; width: 500px; max-height: 80vh; overflow-y: auto; }
        .modal-header { padding: 20px 24px; border-bottom: 1px solid #f0f0f0; }
        .modal-body { padding: 24px; }
        .modal-footer { padding: 16px 24px; border-top: 1px solid #f0f0f0; text-align: right; }
        
        /* 表单 */
        .form-row { margin-bottom: 20px; }
        .form-label { display: block; margin-bottom: 8px; font-weight: 500; color: #333; }
        .form-control { width: 100%; padding: 10px 12px; border: 1px solid #d9d9d9; border-radius: 6px; font-size: 14px; }
        .form-control:focus { outline: none; border-color: #1890ff; box-shadow: 0 0 0 2px rgba(24,144,255,0.2); }
        .form-textarea { min-height: 80px; resize: vertical; }
        
        /* 响应式 */
        @media (max-width: 768px) {
            .content { padding: 16px; }
            .stats-grid { grid-template-columns: repeat(2, 1fr); }
            .channels-grid { grid-template-columns: 1fr; }
            .toolbar { flex-direction: column; gap: 16px; }
            .search-form { flex-wrap: wrap; }
        }
    </style>
</head>
<body>
    <!-- 顶部导航 -->
    <div class="header">
        <div class="logo">🔔 通知平台管理后台</div>
        <div class="header-right">
            <button class="lang-switch">中文 / EN</button>
            <span>管理员</span>
            <button class="btn-link">退出</button>
        </div>
    </div>

    <!-- 主容器 -->
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="menu-item">📝 模板管理</div>
            <div class="menu-item">👥 收件人组管理</div>
            <div class="menu-item">📊 通知审计</div>
            <div class="menu-item active">📡 渠道管理</div>
            <div class="menu-item">💬 站内信管理</div>
            <div class="menu-item">⚙️ 系统设置</div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 页面头部 -->
            <div class="page-header">
                <h1 class="page-title">渠道管理</h1>
                <div class="breadcrumb">首页 / 渠道管理</div>
            </div>

            <!-- 统计概览 -->
            <div class="stats-grid">
                <div class="stat-card stat-total">
                    <div class="stat-trend trend-stable">→ 0%</div>
                    <div class="stat-icon">📡</div>
                    <div class="stat-number">4</div>
                    <div class="stat-label">总渠道数</div>
                </div>
                <div class="stat-card stat-enabled">
                    <div class="stat-trend trend-up">↗ +1</div>
                    <div class="stat-icon">✅</div>
                    <div class="stat-number">4</div>
                    <div class="stat-label">启用渠道</div>
                </div>
                <div class="stat-card stat-disabled">
                    <div class="stat-trend trend-stable">→ 0%</div>
                    <div class="stat-icon">❌</div>
                    <div class="stat-number">0</div>
                    <div class="stat-label">禁用渠道</div>
                </div>
                <div class="stat-card stat-usage">
                    <div class="stat-trend trend-up">↗ +15%</div>
                    <div class="stat-icon">📈</div>
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">今日使用量</div>
                </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
                <div class="search-form">
                    <div class="form-item">
                        <label>渠道名称</label>
                        <input type="text" class="form-input" placeholder="请输入渠道名称">
                    </div>
                    <div class="form-item">
                        <label>状态</label>
                        <select class="form-select">
                            <option value="">全部状态</option>
                            <option value="true">启用</option>
                            <option value="false">禁用</option>
                        </select>
                    </div>
                    <div class="form-item">
                        <label>&nbsp;</label>
                        <button class="btn btn-primary">🔍 搜索</button>
                    </div>
                    <div class="form-item">
                        <label>&nbsp;</label>
                        <button class="btn btn-default">🔄 重置</button>
                    </div>
                </div>
                <div style="display: flex; gap: 12px; align-items: center;">
                    <div class="view-toggle">
                        <button class="view-btn active" onclick="showCardView()">🎯 卡片视图</button>
                        <button class="view-btn" onclick="showTableView()">📋 表格视图</button>
                    </div>
                    <button class="btn btn-primary">➕ 新增渠道</button>
                </div>
            </div>

            <!-- 卡片视图 -->
            <div id="cardView" class="channels-grid">
                <!-- 站内信渠道 -->
                <div class="channel-card">
                    <div class="channel-header">
                        <div class="channel-info">
                            <div class="channel-icon inapp">💬</div>
                            <div class="channel-details">
                                <h3>站内信</h3>
                                <span class="channel-code">IN_APP</span>
                            </div>
                        </div>
                        <div class="channel-status">
                            <div class="status-switch enabled"></div>
                        </div>
                    </div>
                    <div class="channel-stats">
                        <div class="stat-item">
                            <div class="stat-item-number">856</div>
                            <div class="stat-item-label">今日发送</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">99.2%</div>
                            <div class="stat-item-label">成功率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">2ms</div>
                            <div class="stat-item-label">平均延迟</div>
                        </div>
                    </div>
                    <div class="channel-actions">
                        <button class="btn btn-default btn-sm">📊 统计</button>
                        <button class="btn btn-default btn-sm">⚙️ 配置</button>
                        <button class="btn btn-default btn-sm">🧪 测试</button>
                    </div>
                </div>

                <!-- 短信渠道 -->
                <div class="channel-card">
                    <div class="channel-header">
                        <div class="channel-info">
                            <div class="channel-icon sms">📱</div>
                            <div class="channel-details">
                                <h3>短信</h3>
                                <span class="channel-code">SMS</span>
                            </div>
                        </div>
                        <div class="channel-status">
                            <div class="status-switch enabled"></div>
                        </div>
                    </div>
                    <div class="channel-stats">
                        <div class="stat-item">
                            <div class="stat-item-number">234</div>
                            <div class="stat-item-label">今日发送</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">98.5%</div>
                            <div class="stat-item-label">成功率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">1.2s</div>
                            <div class="stat-item-label">平均延迟</div>
                        </div>
                    </div>
                    <div class="channel-actions">
                        <button class="btn btn-default btn-sm">📊 统计</button>
                        <button class="btn btn-default btn-sm">⚙️ 配置</button>
                        <button class="btn btn-default btn-sm">🧪 测试</button>
                    </div>
                </div>

                <!-- 邮件渠道 -->
                <div class="channel-card">
                    <div class="channel-header">
                        <div class="channel-info">
                            <div class="channel-icon email">📧</div>
                            <div class="channel-details">
                                <h3>邮件</h3>
                                <span class="channel-code">EMAIL</span>
                            </div>
                        </div>
                        <div class="channel-status">
                            <div class="status-switch enabled"></div>
                        </div>
                    </div>
                    <div class="channel-stats">
                        <div class="stat-item">
                            <div class="stat-item-number">89</div>
                            <div class="stat-item-label">今日发送</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">97.8%</div>
                            <div class="stat-item-label">成功率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">3.5s</div>
                            <div class="stat-item-label">平均延迟</div>
                        </div>
                    </div>
                    <div class="channel-actions">
                        <button class="btn btn-default btn-sm">📊 统计</button>
                        <button class="btn btn-default btn-sm">⚙️ 配置</button>
                        <button class="btn btn-default btn-sm">🧪 测试</button>
                    </div>
                </div>

                <!-- 企业IM渠道 -->
                <div class="channel-card">
                    <div class="channel-header">
                        <div class="channel-info">
                            <div class="channel-icon im">💼</div>
                            <div class="channel-details">
                                <h3>企业IM</h3>
                                <span class="channel-code">IM</span>
                            </div>
                        </div>
                        <div class="channel-status">
                            <div class="status-switch enabled"></div>
                        </div>
                    </div>
                    <div class="channel-stats">
                        <div class="stat-item">
                            <div class="stat-item-number">55</div>
                            <div class="stat-item-label">今日发送</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">99.1%</div>
                            <div class="stat-item-label">成功率</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-item-number">0.8s</div>
                            <div class="stat-item-label">平均延迟</div>
                        </div>
                    </div>
                    <div class="channel-actions">
                        <button class="btn btn-default btn-sm">📊 统计</button>
                        <button class="btn btn-default btn-sm">⚙️ 配置</button>
                        <button class="btn btn-default btn-sm">🧪 测试</button>
                    </div>
                </div>
            </div>

            <!-- 表格视图 -->
            <div id="tableView" class="table-container" style="display: none;">
                <table class="table">
                    <thead>
                        <tr>
                            <th>渠道信息</th>
                            <th>状态</th>
                            <th>今日发送量</th>
                            <th>成功率</th>
                            <th>平均延迟</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="channel-icon inapp" style="width: 32px; height: 32px; font-size: 14px;">💬</div>
                                    <div>
                                        <div style="font-weight: 500;">站内信</div>
                                        <div style="font-size: 12px; color: #666;">IN_APP</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>856</td>
                            <td>99.2%</td>
                            <td>2ms</td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">配置</button>
                                    <button class="btn btn-link btn-sm">测试</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="channel-icon sms" style="width: 32px; height: 32px; font-size: 14px;">📱</div>
                                    <div>
                                        <div style="font-weight: 500;">短信</div>
                                        <div style="font-size: 12px; color: #666;">SMS</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>234</td>
                            <td>98.5%</td>
                            <td>1.2s</td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">配置</button>
                                    <button class="btn btn-link btn-sm">测试</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="channel-icon email" style="width: 32px; height: 32px; font-size: 14px;">📧</div>
                                    <div>
                                        <div style="font-weight: 500;">邮件</div>
                                        <div style="font-size: 12px; color: #666;">EMAIL</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>89</td>
                            <td>97.8%</td>
                            <td>3.5s</td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">配置</button>
                                    <button class="btn btn-link btn-sm">测试</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div style="display: flex; align-items: center; gap: 12px;">
                                    <div class="channel-icon im" style="width: 32px; height: 32px; font-size: 14px;">💼</div>
                                    <div>
                                        <div style="font-weight: 500;">企业IM</div>
                                        <div style="font-size: 12px; color: #666;">IM</div>
                                    </div>
                                </div>
                            </td>
                            <td><span class="status-tag status-enabled">启用</span></td>
                            <td>55</td>
                            <td>99.1%</td>
                            <td>0.8s</td>
                            <td>2024-01-15 10:30:00</td>
                            <td>
                                <div style="display: flex; gap: 8px;">
                                    <button class="btn btn-link btn-sm">编辑</button>
                                    <button class="btn btn-link btn-sm">配置</button>
                                    <button class="btn btn-link btn-sm">测试</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 新增/编辑渠道模态框 -->
    <div class="modal" id="channelModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>新增渠道</h3>
            </div>
            <div class="modal-body">
                <div class="form-row">
                    <label class="form-label">渠道代码 *</label>
                    <select class="form-control">
                        <option value="">请选择渠道代码</option>
                        <option value="IN_APP">IN_APP - 站内信</option>
                        <option value="SMS">SMS - 短信</option>
                        <option value="EMAIL">EMAIL - 邮件</option>
                        <option value="IM">IM - 企业IM</option>
                    </select>
                </div>
                <div class="form-row">
                    <label class="form-label">渠道名称 *</label>
                    <input type="text" class="form-control" placeholder="请输入渠道名称">
                </div>
                <div class="form-row">
                    <label class="form-label">
                        <input type="checkbox" checked> 启用渠道
                    </label>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-default">取消</button>
                <button class="btn btn-primary">保存</button>
            </div>
        </div>
    </div>

    <script>
        function showCardView() {
            document.getElementById('cardView').style.display = 'grid';
            document.getElementById('tableView').style.display = 'none';
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        function showTableView() {
            document.getElementById('cardView').style.display = 'none';
            document.getElementById('tableView').style.display = 'block';
            document.querySelectorAll('.view-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
