# Server Configuration
server.port=8081
server.servlet.context-path=/notification-admin

# Spring Application Configuration
spring.application.name=notification-admin-bff

# Nacos Discovery Configuration
spring.cloud.nacos.discovery.server-addr=127.0.0.1:8848
spring.cloud.nacos.discovery.namespace=
spring.cloud.nacos.discovery.group=DEFAULT_GROUP
spring.cloud.nacos.discovery.enabled=true

# Database Configuration (Same as notification-service)
# 数据库URL（这里使用阿里云RDS）
spring.datasource.url=***********************************************************************************************************************************
# 数据库用户名
spring.datasource.username=alex
# 数据库密码
spring.datasource.password=Yishao@112
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Connection Pool
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.connection-timeout=30000
spring.datasource.hikari.idle-timeout=600000
spring.datasource.hikari.max-lifetime=1800000

# MyBatis Plus Configuration
mybatis-plus.configuration.map-underscore-to-camel-case=true
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=deleted
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0
mybatis-plus.mapper-locations=classpath:mapper/*.xml

# Logging
logging.level.com.enterprise.notification.admin=DEBUG
logging.level.com.baomidou.mybatisplus=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n

# SpringDoc OpenAPI 3 Configuration
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.operationsSorter=method
springdoc.swagger-ui.tagsSorter=alpha
springdoc.swagger-ui.tryItOutEnabled=true
springdoc.swagger-ui.filter=true

# Notification Client Configuration
# 使用服务名而不是直接URL，通过Nacos服务发现调用
notification.client.base-url=http://notification-service/notification-service
notification.client.connect-timeout=5000
notification.client.read-timeout=30000
